// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/missions/complete": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Mark a specific step in a mission guidance as completed",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "missions"
                ],
                "summary": "Complete a mission step",
                "parameters": [
                    {
                        "description": "Complete mission request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.CompleteMissionDto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Step completed successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/middleware.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad request - validation error",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Mission or step not found",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/missions/me": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Retrieve all mission assignments for the authenticated user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "missions"
                ],
                "summary": "Get user mission assignments",
                "responses": {
                    "200": {
                        "description": "List of mission assignments",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/middleware.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.IncidentGuidance"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/missions/update": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Upload image or video files to document an incident",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "missions"
                ],
                "summary": "Upload incident media files",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Incident ID",
                        "name": "incident_id",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Media files (images or videos, max 10MB each)",
                        "name": "files",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Files uploaded successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/middleware.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad request - invalid file type or size",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/errors.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.CompleteMissionDto": {
            "description": "Request payload for completing a mission step",
            "type": "object",
            "required": [
                "mission_id",
                "step_id"
            ],
            "properties": {
                "mission_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "step_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                }
            }
        },
        "errors.ErrorDetail": {
            "description": "Detailed error information",
            "type": "object",
            "properties": {
                "details": {},
                "message": {
                    "type": "string",
                    "example": "Validation failed"
                },
                "type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/errors.ErrorType"
                        }
                    ],
                    "example": "VALIDATION_ERROR"
                }
            }
        },
        "errors.ErrorResponse": {
            "description": "Standard error response format",
            "type": "object",
            "properties": {
                "error": {
                    "$ref": "#/definitions/errors.ErrorDetail"
                },
                "request_id": {
                    "type": "string",
                    "example": "req-123456"
                },
                "timestamp": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "errors.ErrorType": {
            "type": "string",
            "enum": [
                "VALIDATION_ERROR",
                "NOT_FOUND",
                "UNAUTHORIZED",
                "FORBIDDEN",
                "BAD_REQUEST",
                "CONFLICT",
                "INTERNAL_ERROR",
                "DATABASE_ERROR",
                "EXTERNAL_SERVICE_ERROR",
                "TIMEOUT_ERROR"
            ],
            "x-enum-varnames": [
                "ErrorTypeValidation",
                "ErrorTypeNotFound",
                "ErrorTypeUnauthorized",
                "ErrorTypeForbidden",
                "ErrorTypeBadRequest",
                "ErrorTypeConflict",
                "ErrorTypeInternal",
                "ErrorTypeDatabase",
                "ErrorTypeExternal",
                "ErrorTypeTimeout"
            ]
        },
        "middleware.SuccessResponse": {
            "description": "Standard success response wrapper",
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "0000"
                },
                "data": {},
                "status": {
                    "type": "integer",
                    "example": 200
                }
            }
        },
        "models.Alarm": {
            "description": "Alarm entity triggered by security events",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Fire alarm triggered in main building"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "premise": {
                    "$ref": "#/definitions/models.Premise"
                },
                "premise_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "severity": {
                    "type": "string",
                    "enum": [
                        "low",
                        "medium",
                        "high"
                    ],
                    "example": "high"
                },
                "triggered_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "type": {
                    "type": "string",
                    "example": "fire"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.GuidanceStep": {
            "description": "Individual step within a guidance template procedure",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Quickly evaluate the severity and scope of the fire"
                },
                "guidance_template": {
                    "$ref": "#/definitions/models.GuidanceTemplate"
                },
                "guidance_template_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "step_number": {
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "type": "string",
                    "example": "Assess the situation"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.GuidanceTemplate": {
            "description": "Template containing step-by-step guidance for handling incidents",
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "example": "Emergency"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Standard procedure for handling fire emergencies"
                },
                "guidance_steps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.GuidanceStep"
                    }
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "name": {
                    "type": "string",
                    "example": "Fire Emergency Response"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.Incident": {
            "description": "Security incident entity with status tracking and guidance",
            "type": "object",
            "properties": {
                "alarm": {
                    "$ref": "#/definitions/models.Alarm"
                },
                "alarm_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Fire detected on the 3rd floor of Building A"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "incident_guidance": {
                    "$ref": "#/definitions/models.IncidentGuidance"
                },
                "location": {
                    "type": "string",
                    "example": "Building A, Floor 3"
                },
                "name": {
                    "type": "string",
                    "example": "Fire in Building A"
                },
                "severity": {
                    "type": "string",
                    "enum": [
                        "low",
                        "medium",
                        "high"
                    ],
                    "example": "high"
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "new",
                        "in_progress",
                        "resolved"
                    ],
                    "example": "new"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.IncidentGuidance": {
            "description": "Guidance assignment linking an incident to a guidance template with assignee information",
            "type": "object",
            "properties": {
                "assignee": {
                    "$ref": "#/definitions/models.User"
                },
                "assignee_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "assigner": {
                    "$ref": "#/definitions/models.User"
                },
                "assigner_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "guidance_template": {
                    "$ref": "#/definitions/models.GuidanceTemplate"
                },
                "guidance_template_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "incident": {
                    "$ref": "#/definitions/models.Incident"
                },
                "incident_guidance_steps": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.IncidentGuidanceStep"
                    }
                },
                "incident_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.IncidentGuidanceStep": {
            "description": "Individual step within an incident guidance with completion tracking",
            "type": "object",
            "properties": {
                "completed_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "description": {
                    "type": "string",
                    "example": "Quickly evaluate the severity and scope of the incident"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "incident_guidance": {
                    "$ref": "#/definitions/models.IncidentGuidance"
                },
                "incident_guidance_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "is_completed": {
                    "type": "boolean",
                    "example": false
                },
                "step_number": {
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "type": "string",
                    "example": "Assess the situation"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.Premise": {
            "description": "Premise entity representing physical locations in the system",
            "type": "object",
            "properties": {
                "address": {
                    "type": "string",
                    "example": "123 Main Street, City, Country"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "name": {
                    "type": "string",
                    "example": "Main Building"
                },
                "parent_premise": {
                    "$ref": "#/definitions/models.Premise"
                },
                "parent_premise_id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "models.User": {
            "description": "User entity with authentication and role information",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "format": "uuid",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "name": {
                    "type": "string",
                    "example": "John Doe"
                },
                "role": {
                    "type": "string",
                    "example": "admin"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "SCS Mission Service API",
	Description:      "This is the SCS Mission Service API for managing security incidents and guidance procedures.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
